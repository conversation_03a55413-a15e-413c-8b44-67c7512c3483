# Playwright Button Click Tests

这个项目演示了如何使用 Playwright 编写点击按钮的自动化测试。

## 项目结构

```
project_Study/
├── package.json          # 项目依赖配置
├── playwright.config.ts  # Playwright 配置文件
├── index.html            # 测试页面
├── tests/
│   └── button-click.spec.ts  # 按钮点击测试用例
└── README.md             # 项目说明
```

## 安装依赖

```bash
npm install
npx playwright install
```

## 运行测试

```bash
# 运行所有测试
npm test

# 以有头模式运行测试（可以看到浏览器）
npm run test:headed

# 调试模式运行测试
npm run test:debug

# 使用 UI 模式运行测试
npm run test:ui
```

## 测试用例说明

### 1. 基本按钮点击测试
- 测试点击按钮后文本内容的变化
- 验证按钮的可见性和可用性

### 2. 计数器按钮测试
- 测试多次点击按钮
- 验证计数器的正确递增

### 3. 确认对话框测试
- 测试带有确认对话框的按钮
- 处理浏览器原生对话框

### 4. 禁用按钮测试
- 测试禁用状态的按钮
- 验证禁用按钮不会触发操作

### 5. 多种选择器策略测试
- 演示不同的元素定位方法：
  - 按文本内容定位
  - 按角色定位
  - 按测试ID定位
  - 按CSS选择器定位

## 关键 Playwright 概念

### 元素定位
```typescript
// 通过ID定位
page.locator('#buttonId')

// 通过文本定位
page.getByText('Click Me')

// 通过角色定位
page.getByRole('button', { name: 'Submit' })

// 通过测试ID定位
page.getByTestId('special-button')
```

### 断言
```typescript
// 验证元素可见
await expect(button).toBeVisible();

// 验证元素启用
await expect(button).toBeEnabled();

// 验证文本内容
await expect(element).toHaveText('Expected Text');
```

### 交互操作
```typescript
// 点击按钮
await button.click();

// 强制点击（即使元素被遮挡）
await button.click({ force: true });
```

## 最佳实践

1. **使用语义化选择器**：优先使用 `getByRole`、`getByText` 等语义化选择器
2. **添加等待和断言**：确保元素状态符合预期再进行操作
3. **处理异步操作**：使用适当的等待策略
4. **测试用例独立性**：每个测试用例应该独立运行
5. **清晰的测试描述**：使用描述性的测试名称和注释
