{"name": "playwright-button-test", "version": "1.0.0", "description": "Playwright test project for button clicking", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui"}, "devDependencies": {"@playwright/test": "^1.40.0"}, "keywords": ["playwright", "testing", "automation"], "author": "", "license": "MIT"}