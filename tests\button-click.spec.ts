import { test, expect } from '@playwright/test';

test.describe('Button Click Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the test page before each test
    await page.goto('/index.html');
  });

  test('should click button and verify text change', async ({ page }) => {
    // Locate the button element
    const button = page.locator('#clickButton');
    
    // Verify button is visible and enabled
    await expect(button).toBeVisible();
    await expect(button).toBeEnabled();
    
    // Verify initial state
    const resultText = page.locator('#result');
    await expect(resultText).toHaveText('Not clicked yet');
    
    // Click the button
    await button.click();
    
    // Verify the expected result after clicking
    await expect(resultText).toHaveText('Button was clicked!');
  });

  test('should click button multiple times and count clicks', async ({ page }) => {
    const button = page.locator('#countButton');
    const counter = page.locator('#counter');
    
    // Verify initial counter value
    await expect(counter).toHaveText('0');
    
    // Click button multiple times
    for (let i = 1; i <= 3; i++) {
      await button.click();
      await expect(counter).toHaveText(i.toString());
    }
  });

  test('should handle button with confirmation dialog', async ({ page }) => {
    // Set up dialog handler
    page.on('dialog', async dialog => {
      expect(dialog.type()).toBe('confirm');
      expect(dialog.message()).toBe('Are you sure you want to proceed?');
      await dialog.accept();
    });

    const confirmButton = page.locator('#confirmButton');
    const status = page.locator('#status');
    
    // Click button that triggers confirmation
    await confirmButton.click();
    
    // Verify action was completed
    await expect(status).toHaveText('Action confirmed and completed');
  });

  test('should click disabled button and verify no action', async ({ page }) => {
    const disabledButton = page.locator('#disabledButton');
    const message = page.locator('#disabledMessage');
    
    // Verify button is disabled
    await expect(disabledButton).toBeDisabled();
    
    // Try to click disabled button (should not work)
    await disabledButton.click({ force: true });
    
    // Verify no action occurred
    await expect(message).toHaveText('');
  });

  test('should click button with custom selector strategies', async ({ page }) => {
    // Test different selector strategies
    
    // By text content
    await page.getByText('Click Me').click();
    
    // By role
    await page.getByRole('button', { name: 'Submit' }).click();
    
    // By test id
    await page.getByTestId('special-button').click();
    
    // By CSS selector
    await page.locator('.btn-primary').click();
    
    // Verify all buttons were clicked
    const allClickedMessage = page.locator('#allClicked');
    await expect(allClickedMessage).toHaveText('All buttons clicked successfully!');
  });
});
