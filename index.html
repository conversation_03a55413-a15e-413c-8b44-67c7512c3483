<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Click Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        #disabledButton {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Playwright Button Click Test Page</h1>
    
    <div class="test-section">
        <h2>Basic Button Click Test</h2>
        <button id="clickButton">Click Me!</button>
        <div id="result" class="result">Not clicked yet</div>
    </div>

    <div class="test-section">
        <h2>Counter Button Test</h2>
        <button id="countButton">Count Clicks</button>
        <div class="result">Clicks: <span id="counter">0</span></div>
    </div>

    <div class="test-section">
        <h2>Confirmation Dialog Test</h2>
        <button id="confirmButton">Action with Confirmation</button>
        <div id="status" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Disabled Button Test</h2>
        <button id="disabledButton" disabled>Disabled Button</button>
        <div id="disabledMessage" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Multiple Selector Strategies</h2>
        <button id="textButton">Click Me</button>
        <button id="roleButton" role="button">Submit</button>
        <button id="testIdButton" data-testid="special-button">Test ID Button</button>
        <button class="btn-primary">CSS Class Button</button>
        <div id="allClicked" class="result"></div>
    </div>

    <script>
        // Basic click handler
        document.getElementById('clickButton').addEventListener('click', function() {
            document.getElementById('result').textContent = 'Button was clicked!';
        });

        // Counter click handler
        let clickCount = 0;
        document.getElementById('countButton').addEventListener('click', function() {
            clickCount++;
            document.getElementById('counter').textContent = clickCount;
        });

        // Confirmation dialog handler
        document.getElementById('confirmButton').addEventListener('click', function() {
            if (confirm('Are you sure you want to proceed?')) {
                document.getElementById('status').textContent = 'Action confirmed and completed';
            } else {
                document.getElementById('status').textContent = 'Action cancelled';
            }
        });

        // Multiple selector strategies
        let buttonsClicked = 0;
        const totalButtons = 4;
        
        function checkAllClicked() {
            buttonsClicked++;
            if (buttonsClicked === totalButtons) {
                document.getElementById('allClicked').textContent = 'All buttons clicked successfully!';
            }
        }

        document.getElementById('textButton').addEventListener('click', checkAllClicked);
        document.getElementById('roleButton').addEventListener('click', checkAllClicked);
        document.getElementById('testIdButton').addEventListener('click', checkAllClicked);
        document.querySelector('.btn-primary').addEventListener('click', checkAllClicked);
    </script>
</body>
</html>
